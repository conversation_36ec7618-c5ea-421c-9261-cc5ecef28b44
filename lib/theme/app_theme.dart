import 'package:flutter/widgets.dart';

class AppTheme {
  const AppTheme._();

  static const Color black = Color(0xFF000000);
  static const Color white = Color(0xFFFFFFFF);
  static const Color primaryColor = Color(0xFF2697FF);
  static const Color primaryBlueColor = Color(0xFF1A71DB);
  static const Color secondaryColor = Color(0xFF2A2D3E);
  static const Color scaffoldBgColor = Color(0xFFCCE0F4);
  static const Color blueCardColor = Color(0xFF0047AB);

  static const Color primaryTextColor = Color(0xFF333333);
  static const Color secondaryTextColor = Color(0xFF7C7E89);
  static const Color ternaryTextColor = Color(0xFF5E5E5E);

  static const Color greyRoundBg = Color(0xFFECECEC);

  static const Color orContinueWithColor = Color(0xFF606060);
  static const Color textFieldBorder = Color(0xFFCCCCCC);
  static const Color textFieldHint = Color(0xFF777777);
  static const Color checkboxColor = Color(0xFF8E8E93);

  // Header
  static const Color headerIconBgColor = Color(0xFFE4EBF3);

  static const Color borderColor = Color(0xFFECEBEF);
  static const Color loginBgColor = Color(0xFF1684FF);

  static const Color commissionCardColor = Color(0xFF00409D);
  static const Color commissionDropDownBgColor = Color(0xFF0259D8);
  static const Color commissionCardDarkColor = Color(0xFF002F7A);
  static const Color commissionSalesTextColor = Color(0xFF90B5EA);

  // Table
  static const Color paginationActiveBg = Color(0xFF28569F);
  static const Color paginationInactiveBg = Color(0xFFF5F5F5);
  static const Color searchbarBg = Color(0xFFE4EBF3);
  static const Color pageSummaryLabelColor = Color(0xFFB5B7C0);
  static const Color tableColumnHeaderColor = Color(0xFF7C7E89);
  static const Color viewMoreBlue = Color(0xFF4DA1FF);
  static const Color statusActiveBg = Color(0x6116C098);
  static const Color statusActiveText = Color(0xFF008767);
  static const Color statusInactiveBg = Color(0xFFFFC5C5);
  static const Color statusInactiveText = Color(0xFFDF0404);
  static const Color viewAction = Color(0xFF1A71DB);
  static const Color applyFilterTooltip = Color(0xFFFFF6C5);
  static const Color comboBoxBorder = Color(0xFFCCCCCC);
  static const Color selectedComboBoxBorder = Color(0xFF1A71DB);
  static const Color tableHeaderFont = Color(0xFF7C7E89);
  static const Color tableDataFont = Color(0xFF333333);

  //icon
  static const Color roundIconBgColor = Color(0xFFEEEEEE);
  static const Color roundIconColor = Color(0xFF1A71DB);

  static const Color viewMoreBgColor = Color(0xFFE4E4E4);
  static const Color hierarchyLineColor = Color(0xFF666666);
  static const Color textFieldMandatoryColor = Color(0xFFFF0000);
  static const Color docUploadBgColor = Color(0xFFFAFAFA);

  static const Color breadcrumbParentTextColor = Color(0xFF374151);
  static const Color breadcrumbChildTextColor = Color(0xFF737373);
  static const Color breadcrumbBgColor = Color(0xFFF8FAFE);
  static const Color breadcrumbArrowColor = Color(0xFF666666);
}
