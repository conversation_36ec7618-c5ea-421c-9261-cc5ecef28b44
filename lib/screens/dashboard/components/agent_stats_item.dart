import 'package:flutter/material.dart';
import '../../../theme/app_theme.dart';
import '../../../theme/app_fonts.dart';
import '../../../config/responsive.dart';

class AgentStatsItem extends StatelessWidget {
  final String agentName;
  final String salesCount;
  final String percentage;
  final Color indicatorColor;

  const AgentStatsItem({
    super.key,
    required this.agentName,
    required this.salesCount,
    required this.percentage,
    required this.indicatorColor,
  });

  @override
  Widget build(BuildContext context) {
    final bool isMobile = Responsive.isMobile(context);
    
    return Column(
      children: [
        // Percentage with color indicator
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: indicatorColor,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 6),
            Text(
              percentage,
              style: AppFonts.semiBoldTextStyle(
                isMobile ? 14 : 16,
                color: AppTheme.primaryTextColor,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        // Agent name
        Text(
          agentName,
          style: AppFonts.semiBoldTextStyle(
            isMobile ? 12 : 14,
            color: AppTheme.primaryTextColor,
          ),
        ),
        const SizedBox(height: 4),
        // Sales count
        Text(
          '$salesCount Sales',
          style: AppFonts.regularTextStyle(
            isMobile ? 10 : 12,
            color: Colors.grey,
          ),
        ),
      ],
    );
  }
}
