import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../components/common/dropdown_chip.dart';
import '../../../config/app_strings.dart';
import '../../../config/constants.dart';
import '../../../config/json_consts.dart';
import '../../../models/broker.dart';
import '../../../theme/app_theme.dart';
import '../../../theme/app_fonts.dart';
import '../../../config/responsive.dart';

class SalesByBrokersCard extends StatefulWidget {
  const SalesByBrokersCard({super.key});

  @override
  State<SalesByBrokersCard> createState() => _SalesByBrokersCardState();
}

class _SalesByBrokersCardState extends State<SalesByBrokersCard> {
  late Broker selectedBroker;
  int _currentAgentIndex = 0;

  @override
  void initState() {
    super.initState();
    // Default to first broker
    if (brokers.isNotEmpty) {
      selectedBroker = brokers[0];
    }
  }

  @override
  Widget build(BuildContext context) {
    final bool isMobile = Responsive.isMobile(context);
    final Size size = MediaQuery.of(context).size;
    final isSmallView =
        !Responsive.isMobile(context) &&
        size.width < 1440 &&
        size.width > commissionCardBreakPoint;

    return Container(
      padding: const EdgeInsets.symmetric(vertical: defaultPadding),

      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(isSmallView, size),
          const SizedBox(height: defaultPadding),
          _buildSelectionRow(context, isSmallView),
          // Donut chart
          _donutChart(isMobile),

          // Agent stats row
          _agentStatsRow(isMobile),
          const Divider(
            color: AppTheme.textFieldBorder,
            indent: 0,
            endIndent: 0,
            thickness: 1,
            height: 1,
          ),
          // View More
          GestureDetector(
            onTap: () {},
            child: Padding(
              padding: const EdgeInsets.only(right: defaultPadding, top: 10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Icon(
                    Icons.arrow_forward_ios,
                    color: AppTheme.black,
                    size: isMobile ? 12 : 14,
                  ),
                  const SizedBox(width: 5),
                  Text(
                    viewMore,
                    style: AppFonts.mediumTextStyle(
                      isMobile ? 12 : 14,
                      color: AppTheme.black,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _agentStatsRow(bool isMobile) {
    // Calculate total sales for percentage calculation
    final totalSales = selectedBroker.agents.fold(
      0,
      (sum, agent) => sum + agent.sales,
    );

    // Calculate the max index to start from
    final maxStartIndex = selectedBroker.agents.length > 2
        ? selectedBroker.agents.length - 2
        : 0;

    // Ensure current index is within bounds
    _currentAgentIndex = _currentAgentIndex.clamp(0, maxStartIndex);

    return Container(
      padding: const EdgeInsets.only(
        left: defaultPadding,
        right: defaultPadding,
        bottom: defaultPadding,
      ),
      child: Row(
        children: [
          // Left arrow
          _ArrowButton(
            icon: Icons.chevron_left,
            isMobile: isMobile,
            isEnabled: _currentAgentIndex > 0,
            onTap: _currentAgentIndex > 0
                ? () {
                    setState(() {
                      if (_currentAgentIndex > 0) {
                        _currentAgentIndex--;
                      }
                    });
                  }
                : null,
          ),
          SizedBox(width: isMobile ? 8 : 12),
          Expanded(
            child: Row(
              spacing: 5,
              children: selectedBroker.agents
                  .skip(_currentAgentIndex)
                  .take(2)
                  .map((agent) {
                    final percentage = (agent.sales / totalSales * 100)
                        .toStringAsFixed(0);
                    return Expanded(
                      child: _AgentStat(
                        color: agent.color,
                        percent: "$percentage%",
                        name: agent.name,
                        sales: agent.sales.toString(),
                        isMobile: isMobile,
                      ),
                    );
                  })
                  .toList(),
            ),
          ),
          SizedBox(width: isMobile ? 8 : 12),
          // Right arrow
          _ArrowButton(
            icon: Icons.chevron_right,
            isMobile: isMobile,
            isEnabled: _currentAgentIndex < maxStartIndex,
            onTap: _currentAgentIndex < maxStartIndex
                ? () {
                    setState(() {
                      _currentAgentIndex++;
                    });
                  }
                : null,
          ),
        ],
      ),
    );
  }

  Center _donutChart(bool isMobile) {
    return Center(
      child: Container(
        width: isMobile ? 160 : 200,
        height: isMobile ? 160 : 200,
        child: Stack(
          alignment: Alignment.center,
          children: [
            PieChart(
              PieChartData(
                sectionsSpace: 0,
                centerSpaceRadius: isMobile ? 40 : 50,
                startDegreeOffset: -90,
                sections: _getBrokerSections(isMobile),
              ),
            ),
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  "${selectedBroker.agents.fold(0, (sum, agent) => sum + agent.sales)}",
                  style: AppFonts.semiBoldTextStyle(
                    isMobile ? 28 : 36,
                    color: AppTheme.primaryTextColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  sales,
                  style: AppFonts.regularTextStyle(
                    isMobile ? 14 : 16,
                    color: AppTheme.primaryTextColor.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  List<PieChartSectionData> _getBrokerSections(bool isMobile) {
    // Calculate total sales for percentage calculation
    final totalSales = selectedBroker.agents.fold(
      0,
      (sum, agent) => sum + agent.sales,
    );

    // Create sections for the first 4 brokers (or fewer if there are less than 4)
    final sections = <PieChartSectionData>[];

    for (int i = 0; i < selectedBroker.agents.length && i < 4; i++) {
      final agent = selectedBroker.agents[i];
      final percentage = agent.sales / totalSales * 100;

      sections.add(
        PieChartSectionData(
          color: agent.color,
          value: percentage,
          showTitle: false,
          radius: isMobile ? 12 : 16,
        ),
      );
    }

    return sections;
  }

  Widget _buildHeader(bool isSmallView, Size size) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: defaultPadding),

      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            salesByBrokers,
            style: AppFonts.semiBoldTextStyle(
              18,
              color: AppTheme.primaryTextColor,
            ),
          ),
          if (!isSmallView)
            DropdownChip(
              label: size.width < 1800 ? monthLabel : selectMonth,
              bgColor: AppTheme.scaffoldBgColor,
              textColor: AppTheme.primaryTextColor,
            ),
        ],
      ),
    );
  }

  Widget _buildSelectionRow(BuildContext context, bool isSmallView) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: defaultPadding),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: DropdownChip(
              label:
                  selectedBroker?.name ??
                  (Responsive.isMobile(context) && isSmallView
                      ? broker1Label
                      : selectBroker1),
              expandText: true,
              bgColor: AppTheme.scaffoldBgColor,
              textColor: AppTheme.primaryTextColor,
              isBrokerSelector: true,
              onBrokerSelected: (broker) {
                setState(() {
                  selectedBroker = broker;
                });
              },
            ),
          ),
          if (!Responsive.isMobile(context) && isSmallView)
            const SizedBox(width: 8),
          if (!Responsive.isMobile(context) && isSmallView)
            Expanded(
              child: DropdownChip(
                label: monthLabel,
                expandText: true,
                bgColor: AppTheme.scaffoldBgColor,
                textColor: AppTheme.primaryTextColor,
              ),
            ),
        ],
      ),
    );
  }
}

// Arrow button widget
class _ArrowButton extends StatelessWidget {
  final IconData icon;
  final bool isMobile;
  final bool isEnabled;
  final VoidCallback? onTap;

  const _ArrowButton({
    required this.icon,
    required this.isMobile,
    required this.isEnabled,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(isMobile ? 6 : 8),
        decoration: BoxDecoration(
          color: AppTheme.greyRoundBg,
          shape: BoxShape.circle,
          border: Border.all(
            color: isEnabled ? Colors.grey.shade200 : Colors.grey.shade100,
            width: 1,
          ),
        ),
        child: Icon(
          icon,
          size: isMobile ? 14 : 16,
          color: isEnabled
              ? AppTheme.primaryTextColor
              : AppTheme.primaryTextColor.withValues(alpha: 0.3),
        ),
      ),
    );
  }
}

// Agent stat widget
class _AgentStat extends StatelessWidget {
  final Color color;
  final String percent;
  final String name;
  final String sales;
  final bool isMobile;

  const _AgentStat({
    required this.color,
    required this.percent,
    required this.name,
    required this.sales,
    required this.isMobile,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: isMobile ? 28 : 32,
            height: isMobile ? 28 : 32,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(5),
            ),
            child: Center(
              child: Text(
                percent,
                style: AppFonts.regularTextStyle(
                  isMobile ? 10 : 12,
                  color: Colors.white,
                ),
              ),
            ),
          ),
          SizedBox(width: isMobile ? 6 : 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  name,
                  style: AppFonts.semiBoldTextStyle(
                    isMobile ? 10 : 12,
                    color: AppTheme.primaryTextColor,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
                const SizedBox(height: 5),
                RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text: sales,
                        style: AppFonts.semiBoldTextStyle(
                          isMobile ? 10 : 12,
                          color: AppTheme.primaryTextColor,
                        ),
                      ),
                      TextSpan(
                        text: " Sales",
                        style: AppFonts.regularTextStyle(
                          isMobile ? 9 : 10,
                          color: AppTheme.primaryTextColor,
                        ),
                      ),
                    ],
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
