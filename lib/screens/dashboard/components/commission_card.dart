import 'package:flutter/material.dart';
import '../../../config/app_strings.dart';
import '../../../models/broker.dart';
import '../../../theme/app_theme.dart';
import '/config/constants.dart';
import '/config/responsive.dart';
import '../../../theme/app_fonts.dart';
import '../../../components/common/dropdown_chip.dart';
import '../../../config/json_consts.dart';
import 'view_more_button.dart';

class CommissionCard extends StatefulWidget {
  const CommissionCard({super.key});

  @override
  State<CommissionCard> createState() => _CommissionCardState();
}

class _CommissionCardState extends State<CommissionCard> {
  late Broker selectedBroker;

  @override
  void initState() {
    super.initState();
    // Default to first broker
    if (brokers.isNotEmpty) {
      selectedBroker = brokers[0];
    }
  }

  @override
  Widget build(BuildContext context) {
    final Size size = MediaQuery.of(context).size;
    final isSmallView =
        !Responsive.isMobile(context) &&
        size.width < 1440 &&
        size.width > commissionCardBreakPoint;

    return Container(
      constraints: BoxConstraints(maxHeight: size.height * 0.65),
      padding: const EdgeInsets.symmetric(vertical: defaultPadding),
      decoration: _buildBoxDecoration(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(isSmallView, size),
          const SizedBox(height: defaultPadding),
          _buildSelectionRow(context, isSmallView),
          const SizedBox(height: defaultPadding),
          _buildAgentCommissions(isSmallView),
          const Divider(
            color: AppTheme.primaryColor,
            indent: 0,
            endIndent: 0,
            thickness: 1,
          ),
          _buildTotalRow(),
        ],
      ),
    );
  }

  BoxDecoration _buildBoxDecoration() {
    return BoxDecoration(
      color: AppTheme.commissionCardColor,
      borderRadius: BorderRadius.circular(15),
      boxShadow: [
        BoxShadow(
          color: Colors.black.withValues(alpha: 0.26),
          blurRadius: 5,
          offset: const Offset(0, 2),
        ),
      ],
    );
  }

  Widget _buildHeader(bool isSmallView, Size size) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: defaultPadding),

      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            grossCommission,
            style: AppFonts.semiBoldTextStyle(18, color: Colors.white),
          ),
          if (!isSmallView)
            DropdownChip(label: size.width < 1800 ? monthLabel : selectMonth),
        ],
      ),
    );
  }

  Widget _buildSelectionRow(BuildContext context, bool isSmallView) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: defaultPadding),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: DropdownChip(
              label:
                  selectedBroker?.name ??
                  (Responsive.isMobile(context) && isSmallView
                      ? broker1Label
                      : selectBroker1),
              expandText: true,
              //  textColor: Colors.white,
              isBrokerSelector: true,
              onBrokerSelected: (broker) {
                setState(() {
                  selectedBroker = broker;
                });
              },
            ),
          ),
          if (!Responsive.isMobile(context) && isSmallView)
            const SizedBox(width: 8),
          if (!Responsive.isMobile(context) && isSmallView)
            Expanded(child: DropdownChip(label: monthLabel, expandText: true)),
        ],
      ),
    );
  }

  Widget _buildAgentCommissions(bool isSmallView) {
    return Expanded(
      child: ListView.builder(
        shrinkWrap: true,
        // physics: const NeverScrollableScrollPhysics(),
        padding: const EdgeInsets.symmetric(horizontal: defaultPadding),
        itemCount: selectedBroker.agents.length,
        itemBuilder: (context, index) {
          final e = selectedBroker.agents[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: _buildAgentCommissionItem(
              e.name,
              e.sales,
              e.amount,
              index % 2 == 0 ? true : false,
              isSmallView: isSmallView,
            ),
          );
        },
      ),
    );
  }

  Widget _buildAgentCommissionItem(
    String name,
    int sales,
    double amount,
    bool isDarkTheme, {
    required bool isSmallView,
  }) {
    final double imageSize = isSmallView ? 30 : 35;
    final double agentFontSize = isSmallView ? 13 : 15;
    final double saleFontSize = isSmallView ? 11 : 13;
    final double amountFontSize = isSmallView ? 14 : 16;
    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 3),
      decoration: BoxDecoration(
        color: isDarkTheme
            ? AppTheme.commissionCardDarkColor
            : Colors.transparent,
        borderRadius: BorderRadius.circular(25),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Image.asset(
            '$iconAssetpath/commission_agent.png',
            height: imageSize,
            width: imageSize,
          ),
          const SizedBox(width: 10),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  name,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: AppFonts.mediumTextStyle(
                    agentFontSize,
                    color: Colors.white,
                  ),
                ),
                Text(
                  sales.toString() + ' sales',
                  style: AppFonts.mediumTextStyle(
                    saleFontSize,
                    color: AppTheme.commissionSalesTextColor,
                  ),
                ),
              ],
            ),
          ),
          SizedBox(width: 5),
          Text(
            "\$" + amount.toString(),
            style: AppFonts.semiBoldTextStyle(
              amountFontSize,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTotalRow() {
    return Padding(
      padding: const EdgeInsets.symmetric(
        vertical: 8.0,
        horizontal: defaultPadding,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          ViewMoreButton(
            onPressed: () {},
            text: viewMore,
            iconSize: 16,
            fontSize: 16,
            spacing: 5,
          ),
          Text(
            dollar98200,
            style: AppFonts.semiBoldTextStyle(16, color: Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildViewMoreButton() {
    return Center(
      child: TextButton.icon(
        onPressed: () {},
        icon: const Icon(Icons.visibility, color: Colors.white, size: 16),
        label: Text(
          viewMore,
          style: AppFonts.normalTextStyle(14, color: Colors.white),
        ),
      ),
    );
  }
}
