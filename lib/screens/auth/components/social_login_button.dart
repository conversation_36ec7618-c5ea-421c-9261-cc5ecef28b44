import 'package:flutter/material.dart';
import '/config/responsive.dart';
import '/theme/app_fonts.dart';
import '../../../theme/app_theme.dart';

class SocialLoginButton extends StatelessWidget {
  final Widget icon;
  final String text;
  final VoidCallback onPressed;

  const SocialLoginButton({
    super.key,
    required this.icon,
    required this.text,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: Responsive.isMobile(context) ? 50 : 45,
      child: OutlinedButton(
        onPressed: onPressed,
        style: OutlinedButton.styleFrom(
          backgroundColor: AppTheme.greyRoundBg,
          side: BorderSide.none,
          padding: EdgeInsets.symmetric(
            horizontal: Responsive.isMobile(context) ? 16 : 12,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            icon,
            SizedBox(width: Responsive.isMobile(context) ? 12 : 8),
            Flexible(
              child: Text(
                text,
                style: AppFonts.mediumTextStyle(
                  Responsive.isMobile(context) ? 15 : 13,
                  color: AppTheme.primaryTextColor,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
