enum UserRole { platformOwner, admin, broker, agent }

// Helper to get display string
String userRoleToString(UserRole role) {
  switch (role) {
    case UserRole.platformOwner:
      return "Platform Owner";
    case UserRole.admin:
      return "Admin";
    case UserRole.broker:
      return "Broker";
    case UserRole.agent:
      return "Agent";
  }
}

UserRole stringToUserRole(String role) {
  switch (role) {
    case "Platform Owner":
      return UserRole.platformOwner;
    case "Admin":
      return UserRole.admin;
    case "Broker":
      return UserRole.broker;
    case "Agent":
      return UserRole.agent;
    default:
      return UserRole.platformOwner;
  }
}
