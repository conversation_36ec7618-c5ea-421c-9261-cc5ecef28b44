// App General
const String appName = "NeoRevv";
const String appDescription =
    "A powerful tool to manage agents, track commissions, and streamline real estate operations.";
const String appDescriptionP1 = 'A ';
const String appDescriptionP2 = 'powerful tool ';
const String appDescriptionP3 =
    'to manage agents, track commissions, and streamline real estate operations.';

// Login Screen
const String loginTitle = "Login";
const String signInWithGmail = "Sign in with Gmail";
const String signInWithApple = "Sign in with Apple";
const String emailHint = "Enter your email";
const String passwordHint = "Enter your password";
const String rememberMe = "Remember me";
const String forgotPassword = "Forgot password?";
const String loginButton = "Login";
const String dontHaveAccount = "Don't you have an account? ";
const String signUp = "Sign up";
const String orContinueWith = "Or with email";

// Dashboard
const String brokersTitle = "Brokers";
const String searchHint = "Search by name";
const String brokerColumnHeader = "Broker Name";
const String contactsColumnHeader = "Contacts";
const String emailAddressColumnHeader = "Email Address";
const String totalAgentsColumnHeader = "Total Agents";
const String totalSalesColumnHeader = "Total Sales";
const String actionsColumnHeader = "Actions";
const String viewDocumentsLabel = "View documents";
const String showingDataLabelP1 = "Showing data";
const String showingDataLabelP2 = "entries";
const String toLabel = "to";
const String ofLabel = "of";

const String dashboardTab = "Dashboard";
const String brokersTab = "Brokers";
const String agentsTab = "Agents";
const String salesTab = "Sales";
const String commissionTab = "Commission";
const String reportsTab = "Reports";

const String addNewButton = "Add New";
const String welcomeLabel = 'Welcome, ';
const String platformOwnerLabel = 'Platform Owner';
const String copyright = 'Copyright © 2025 NeoRevv';
const String homeFooterLabel = 'Home';
const String privacyPolicy = 'Privacy Policy';
const String termsAndConditions = 'Terms and conditions';
const String viewDocuments = "View documents";
const String viewAgents = "View Agents";

// Sales by Brokers Card
const String salesByBrokers = 'Sales by Brokers';
const String selectMonth = 'Select Month';
const String sales = 'Sales';
const String viewMore = 'View More';
const String viewLess = 'View Less';

const grossCommission = "Gross Commission";
const monthLabel = "Month";

// TO DO: Remove after API integration
const broker1Label = "Broker1";
const selectBroker1 = "Select Broker 1";
//
const viewAllBrokers = "view all brokers";
const dollar98200 = "\$98200";

// Agent Network Hierarchy
const String agentHierarchy = "Agent Hierarchy";
const String agentNetwork = "Agent Network";
const String totalSalesRevenue = "Total Sales Revenue";
const String totalCommission = "Total Commission";
const String recruitsCount = "Recruits Count ";
const String recruitsMobile = "Recruits ";
const String viewProfile = "View Profile";
const String agentsRecruitedBy = "Agents recruited by";
const String broker = "Broker";
const String agent = "Agent";
const String noRecruitsFound = "No recruits found";
const String hasNotRecruitedAnyAgentsYet = "hasn't recruited any agents yet.";

// Agent Screen
const String agents = "Agents";
const String agentName = "Agent Name";
const String agentContact = "Contacts";
const String agentEmail = "Email Address";
const String agentJoinDate = "Join Date";
const String agentState = "State";
const String agentCity = "City";
const String agentLevel = "Level";
const String agentTotalDeals = "Total Deals";
const String agentEarning = "Earning";
const String agentStatus = "Status";
const String actions = "Actions";
const String filter = "Filter";
const String active = "Active";
const String inactive = "Inactive";
const String searchAgent = "Search";
const String filterBy = "Filter by";
const String selectAgent = "Select Agent";
const String selectLevel = "Select Level";
const String selectStatus = "Select Status";
const String apply = "Apply";

// Broker Registration Screen
const String dashboardAdmin = 'Dashboard Admin';
const String addNewBroker = 'Add New Broker';

const String brokerInformation = 'Broker Information';
const String firstName = 'First Name';
const String lastName = 'Last Name';
const String phone = 'Phone';
const String email = 'Email';
const String company = 'Company';
const String city = 'City';
const String stateProvince = 'State/Province';
const String postalZipCode = 'Postal/Zip Code';
const String country = 'Country';
const String enterFirstName = 'Enter your first name';
const String enterLastName = 'Enter your last name';
const String enterPhone = 'Enter your phone number';
const String enterEmail = 'Enter your email address';
const String enterCompany = 'Enter your company name';
const String uploadDocuments = 'Upload Documents';
const String eoInsuranceCertificate =
    'E&O Insurance Certificate (Errors and Omissions)';
const String brokerageLicense = 'Brokerage License';
const String principalBrokerId = 'Principal Broker ID';
const String logo = 'Logo';
const String chooseFileOrDragDrop = 'Choose a file or drag & drop it here.';
const String pdfOrImageOnly = 'PDF or image formats only. Max 20 MB.';
const String chooseImageOrDragDrop = 'Choose image or drag & drop it here.';
const String imageFormatsOnly = 'JPG, JPEG, PNG and WEBP. Max 20 MB.';
const String clear = 'Clear';
const String register = 'Register';
const String inviteBroker = 'Invite Broker';
const String registerBroker = 'Register Broker';
const String upload = 'Upload';

const String switchTab = 'Switch Tab';
const String switchTabConfirmation =
    'Are you sure you want to switch tabs? All the entered data in the form will be lost.';
const String clearData = 'Clear Data';
const String clearDataConfirmation =
    'Are you sure you want to clear the form?\nAll entered data will be lost, and you will need to re-enter it.';
const String cancel = 'Cancel';
const String ok = 'OK';
const String processingData = 'Processing Data...';

// Validation
const String thisFieldIsRequired = 'This field is required';
const String phoneNumberIsRequired = 'Phone number is required';
const String emailIsRequired = 'Email is required';
const String invalidEmail = 'Please enter a valid email address';
const String invalidPhone = 'Please enter a valid 10-digit US phone number';
const String pleaseFillRequiredFields =
    'Please fill all required fields correctly';
